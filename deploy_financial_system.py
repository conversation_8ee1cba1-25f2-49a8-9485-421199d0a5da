#!/usr/bin/env python3
"""
财务系统完整部署脚本
一键部署财务管理系统的所有组件
"""

import sys
import os
import subprocess
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def print_header(title):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_step(step_num, title):
    """打印步骤"""
    print(f"\n📋 步骤 {step_num}: {title}")
    print("-" * 40)

def run_sql_script(script_path, description):
    """运行SQL脚本"""
    print(f"🔄 执行 {description}...")
    
    if not os.path.exists(script_path):
        print(f"❌ SQL脚本不存在: {script_path}")
        return False
    
    try:
        # 这里需要根据实际环境配置数据库连接
        print(f"📄 SQL脚本位置: {script_path}")
        print("⚠️  请手动执行此SQL脚本，或配置数据库连接参数")
        
        # 如果有数据库连接配置，可以使用以下代码自动执行
        # result = subprocess.run([
        #     'sqlcmd', '-S', 'server_name', '-d', 'database_name', 
        #     '-i', script_path
        # ], capture_output=True, text=True)
        # 
        # if result.returncode == 0:
        #     print(f"✅ {description} 完成")
        #     return True
        # else:
        #     print(f"❌ {description} 失败: {result.stderr}")
        #     return False
        
        return True
        
    except Exception as e:
        print(f"❌ 执行SQL脚本失败: {str(e)}")
        return False

def run_python_script(script_path, description):
    """运行Python脚本"""
    print(f"🔄 执行 {description}...")
    
    if not os.path.exists(script_path):
        print(f"❌ Python脚本不存在: {script_path}")
        return False
    
    try:
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ {description} 完成")
            if result.stdout:
                print("输出:")
                print(result.stdout)
            return True
        else:
            print(f"❌ {description} 失败")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行Python脚本失败: {str(e)}")
        return False

def check_prerequisites():
    """检查部署前提条件"""
    print_step(0, "检查部署前提条件")
    
    # 检查必要文件是否存在
    required_files = [
        'migrations/financial_system_init.sql',
        'migrations/financial_basic_subjects.sql',
        'migrations/financial_module_visibility_init.sql',
        'migrations/financial_module_visibility_init.py',
        'migrations/financial_permissions_init.py',
        'test_financial_system.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"✅ {file_path}")
    
    if missing_files:
        print(f"\n❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    # 检查Python环境
    try:
        from app import create_app
        print("✅ Flask应用可以正常导入")
    except Exception as e:
        print(f"❌ Flask应用导入失败: {str(e)}")
        return False
    
    print("✅ 所有前提条件检查通过")
    return True

def deploy_financial_system():
    """部署财务系统"""
    
    print_header("学校食堂财务管理系统部署工具")
    print(f"部署时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败，请解决问题后重试")
        return False
    
    success_steps = 0
    total_steps = 6
    
    # 步骤1: 创建数据库表结构
    print_step(1, "创建财务系统数据库表结构")
    if run_sql_script('migrations/financial_system_init.sql', '财务系统表结构创建'):
        success_steps += 1
    
    # 步骤2: 初始化基础会计科目
    print_step(2, "初始化基础会计科目")
    if run_sql_script('migrations/financial_basic_subjects.sql', '基础会计科目初始化'):
        success_steps += 1
    
    # 步骤3: 设置模块可见性（SQL方式）
    print_step(3, "设置财务模块可见性（SQL方式）")
    if run_sql_script('migrations/financial_module_visibility_init.sql', '财务模块可见性设置'):
        success_steps += 1
    
    # 步骤4: 设置模块可见性（Python方式，备选）
    print_step(4, "设置财务模块可见性（Python方式）")
    if run_python_script('migrations/financial_module_visibility_init.py', '财务模块可见性设置'):
        success_steps += 1
    
    # 步骤5: 初始化财务权限
    print_step(5, "初始化财务系统权限")
    if run_python_script('migrations/financial_permissions_init.py', '财务权限初始化'):
        success_steps += 1
    
    # 步骤6: 系统验证
    print_step(6, "验证财务系统部署")
    if run_python_script('test_financial_system.py', '财务系统验证'):
        success_steps += 1
    
    # 部署结果
    print_header("部署结果")
    print(f"📊 完成步骤: {success_steps}/{total_steps}")
    
    if success_steps == total_steps:
        print("🎉 财务系统部署成功！")
        show_post_deployment_guide()
        return True
    else:
        print("⚠️  部分步骤未完成，请检查错误信息")
        show_troubleshooting_guide()
        return False

def show_post_deployment_guide():
    """显示部署后指南"""
    print("\n📚 部署后操作指南:")
    print("1. 重启Web应用服务")
    print("2. 访问后台管理 -> 系统 -> 模块可见性管理")
    print("   - 根据需要调整各角色的财务模块可见性")
    print("3. 访问后台管理 -> 系统 -> 角色权限管理")
    print("   - 为角色分配相应的财务权限")
    print("4. 为用户分配合适的角色")
    print("5. 测试财务系统功能")
    
    print("\n🔗 财务系统访问地址:")
    print("   - 财务管理首页: /financial/reports")
    print("   - 会计科目管理: /financial/accounting-subjects")
    print("   - 财务凭证管理: /financial/vouchers")
    print("   - 应付账款管理: /financial/payables")
    print("   - 付款记录管理: /financial/payments")
    
    print("\n💡 使用建议:")
    print("1. 首次使用前，请先检查会计科目设置")
    print("2. 从入库单生成应付账款，建立财务流程")
    print("3. 定期查看财务报表，了解经营状况")
    print("4. 建议定期备份财务数据")

def show_troubleshooting_guide():
    """显示故障排除指南"""
    print("\n🔧 故障排除指南:")
    print("1. 数据库连接问题:")
    print("   - 检查数据库服务是否运行")
    print("   - 验证数据库连接字符串")
    print("   - 确认数据库用户权限")
    
    print("\n2. 权限问题:")
    print("   - 确保数据库用户有创建表的权限")
    print("   - 检查应用是否有写入权限")
    
    print("\n3. 模块导入问题:")
    print("   - 检查Python环境和依赖包")
    print("   - 确认项目路径配置正确")
    
    print("\n4. 如需帮助，请提供:")
    print("   - 错误信息截图")
    print("   - 系统环境信息")
    print("   - 数据库版本信息")

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("财务系统部署工具")
        print("\n用法:")
        print("  python deploy_financial_system.py        # 完整部署")
        print("  python deploy_financial_system.py --help # 显示帮助")
        print("\n说明:")
        print("  此工具将自动部署财务管理系统的所有组件")
        print("  包括数据库表、基础数据、权限设置等")
        return
    
    try:
        success = deploy_financial_system()
        if success:
            print("\n🎉 财务系统部署完成！")
            sys.exit(0)
        else:
            print("\n❌ 部署过程中遇到问题")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  部署被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 部署过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
