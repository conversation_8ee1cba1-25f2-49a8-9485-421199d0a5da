-- =============================================
-- 财务系统权限配置脚本
-- 为现有角色添加财务模块权限
-- =============================================

USE [StudentsCMSSP]
GO

PRINT '开始配置财务系统权限...'
PRINT '========================================'

-- =============================================
-- 更新系统管理员权限（拥有所有财务权限）
-- =============================================

PRINT '更新系统管理员权限...'

UPDATE roles 
SET permissions = JSON_MODIFY(
    ISNULL(permissions, '{}'),
    '$.财务管理',
    JSON_QUERY('["view", "create", "edit", "delete", "approve", "audit"]')
)
WHERE name = '系统管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.会计科目管理',
    JSON_QUERY('["view", "create", "edit", "delete"]')
)
WHERE name = '系统管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.财务凭证管理',
    JSON_QUERY('["view", "create", "edit", "delete", "review", "post"]')
)
WHERE name = '系统管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.应付账款管理',
    JSON_QUERY('["view", "create", "edit", "delete", "payment"]')
)
WHERE name = '系统管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.收入管理',
    JSON_QUERY('["view", "create", "edit", "delete", "review"]')
)
WHERE name = '系统管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.成本核算',
    JSON_QUERY('["view", "calculate", "review", "adjust"]')
)
WHERE name = '系统管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.财务报表',
    JSON_QUERY('["view", "export", "print", "config"]')
)
WHERE name = '系统管理员'

PRINT '  ✓ 系统管理员权限更新完成'

-- =============================================
-- 更新超级管理员权限（拥有大部分财务权限）
-- =============================================

PRINT '更新超级管理员权限...'

UPDATE roles 
SET permissions = JSON_MODIFY(
    ISNULL(permissions, '{}'),
    '$.财务管理',
    JSON_QUERY('["view", "create", "edit", "approve", "audit"]')
)
WHERE name = '超级管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.会计科目管理',
    JSON_QUERY('["view"]')
)
WHERE name = '超级管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.财务凭证管理',
    JSON_QUERY('["view", "create", "edit", "review"]')
)
WHERE name = '超级管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.应付账款管理',
    JSON_QUERY('["view", "create", "edit", "payment"]')
)
WHERE name = '超级管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.收入管理',
    JSON_QUERY('["view", "create", "edit", "review"]')
)
WHERE name = '超级管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.成本核算',
    JSON_QUERY('["view", "calculate", "review"]')
)
WHERE name = '超级管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.财务报表',
    JSON_QUERY('["view", "export", "print"]')
)
WHERE name = '超级管理员'

PRINT '  ✓ 超级管理员权限更新完成'

-- =============================================
-- 更新学校管理员权限（基础财务权限）
-- =============================================

PRINT '更新学校管理员权限...'

UPDATE roles 
SET permissions = JSON_MODIFY(
    ISNULL(permissions, '{}'),
    '$.财务管理',
    JSON_QUERY('["view", "approve"]')
)
WHERE name = '学校管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.会计科目管理',
    JSON_QUERY('["view"]')
)
WHERE name = '学校管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.财务凭证管理',
    JSON_QUERY('["view", "review"]')
)
WHERE name = '学校管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.应付账款管理',
    JSON_QUERY('["view", "approve"]')
)
WHERE name = '学校管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.收入管理',
    JSON_QUERY('["view", "review"]')
)
WHERE name = '学校管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.成本核算',
    JSON_QUERY('["view", "review"]')
)
WHERE name = '学校管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.财务报表',
    JSON_QUERY('["view", "export", "print"]')
)
WHERE name = '学校管理员'

PRINT '  ✓ 学校管理员权限更新完成'

-- =============================================
-- 创建财务专员角色
-- =============================================

PRINT '创建财务专员角色...'

IF NOT EXISTS (SELECT 1 FROM roles WHERE name = '财务专员')
BEGIN
    INSERT INTO roles (name, description, permissions, created_at)
    VALUES (
        '财务专员',
        '负责日常财务记账、凭证管理和基础财务报表',
        JSON_QUERY('{
            "财务管理": ["view", "create", "edit"],
            "会计科目管理": ["view"],
            "财务凭证管理": ["view", "create", "edit"],
            "应付账款管理": ["view", "create", "edit"],
            "收入管理": ["view", "create", "edit"],
            "成本核算": ["view", "calculate"],
            "财务报表": ["view", "export"],
            "库存管理": ["view"],
            "采购管理": ["view"]
        }'),
        GETDATE()
    )
    PRINT '  ✓ 财务专员角色创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务专员角色已存在'
END

-- =============================================
-- 创建财务主管角色
-- =============================================

PRINT '创建财务主管角色...'

IF NOT EXISTS (SELECT 1 FROM roles WHERE name = '财务主管')
BEGIN
    INSERT INTO roles (name, description, permissions, created_at)
    VALUES (
        '财务主管',
        '负责财务审核、成本分析和高级财务报表',
        JSON_QUERY('{
            "财务管理": ["view", "create", "edit", "approve", "audit"],
            "会计科目管理": ["view", "create", "edit"],
            "财务凭证管理": ["view", "create", "edit", "review", "post"],
            "应付账款管理": ["view", "create", "edit", "payment", "approve"],
            "收入管理": ["view", "create", "edit", "review"],
            "成本核算": ["view", "calculate", "review", "adjust"],
            "财务报表": ["view", "export", "print", "config"],
            "库存管理": ["view", "approve"],
            "采购管理": ["view", "approve"]
        }'),
        GETDATE()
    )
    PRINT '  ✓ 财务主管角色创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务主管角色已存在'
END

-- =============================================
-- 创建出纳员角色
-- =============================================

PRINT '创建出纳员角色...'

IF NOT EXISTS (SELECT 1 FROM roles WHERE name = '出纳员')
BEGIN
    INSERT INTO roles (name, description, permissions, created_at)
    VALUES (
        '出纳员',
        '负责现金和银行存款管理、收付款记录',
        JSON_QUERY('{
            "财务管理": ["view"],
            "财务凭证管理": ["view"],
            "应付账款管理": ["view", "payment"],
            "收入管理": ["view", "create", "edit"],
            "成本核算": ["view"],
            "财务报表": ["view"],
            "库存管理": ["view"],
            "采购管理": ["view"]
        }'),
        GETDATE()
    )
    PRINT '  ✓ 出纳员角色创建成功'
END
ELSE
BEGIN
    PRINT '  - 出纳员角色已存在'
END

-- =============================================
-- 更新食堂管理员权限（添加基础财务查看权限）
-- =============================================

PRINT '更新食堂管理员权限...'

UPDATE roles 
SET permissions = JSON_MODIFY(
    ISNULL(permissions, '{}'),
    '$.财务管理',
    JSON_QUERY('["view"]')
)
WHERE name = '食堂管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.成本核算',
    JSON_QUERY('["view"]')
)
WHERE name = '食堂管理员'

UPDATE roles 
SET permissions = JSON_MODIFY(
    permissions,
    '$.财务报表',
    JSON_QUERY('["view"]')
)
WHERE name = '食堂管理员'

PRINT '  ✓ 食堂管理员权限更新完成'

-- =============================================
-- 显示权限配置结果
-- =============================================

PRINT ''
PRINT '权限配置完成！当前角色及其财务权限：'
PRINT '========================================'

SELECT 
    name AS '角色名称',
    description AS '角色描述',
    CASE 
        WHEN JSON_VALUE(permissions, '$.财务管理') IS NOT NULL 
        THEN '有财务权限' 
        ELSE '无财务权限' 
    END AS '财务权限状态'
FROM roles 
WHERE name IN ('系统管理员', '超级管理员', '学校管理员', '财务专员', '财务主管', '出纳员', '食堂管理员')
ORDER BY 
    CASE name 
        WHEN '系统管理员' THEN 1
        WHEN '超级管理员' THEN 2
        WHEN '财务主管' THEN 3
        WHEN '财务专员' THEN 4
        WHEN '出纳员' THEN 5
        WHEN '学校管理员' THEN 6
        WHEN '食堂管理员' THEN 7
        ELSE 8
    END

PRINT ''
PRINT '财务系统权限配置完成！'
PRINT '========================================'
