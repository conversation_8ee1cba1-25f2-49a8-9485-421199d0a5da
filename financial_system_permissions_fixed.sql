-- =============================================
-- 财务系统权限配置脚本（修复版）
-- 使用字符串操作避免JSON_MODIFY中文路径问题
-- =============================================

USE [StudentsCMSSP]
GO

PRINT '开始配置财务系统权限（修复版）...'
PRINT '========================================'

-- =============================================
-- 更新系统管理员权限
-- =============================================

PRINT '更新系统管理员权限...'

DECLARE @system_admin_permissions NVARCHAR(MAX)
SELECT @system_admin_permissions = ISNULL(permissions, '{}') FROM roles WHERE name = '系统管理员'

-- 如果是空的JSON对象，直接设置完整权限
IF @system_admin_permissions = '{}' OR @system_admin_permissions IS NULL OR @system_admin_permissions = ''
BEGIN
    SET @system_admin_permissions = '{
        "财务管理": ["view", "create", "edit", "delete", "approve", "audit"],
        "会计科目管理": ["view", "create", "edit", "delete"],
        "财务凭证管理": ["view", "create", "edit", "delete", "review", "post"],
        "应付账款管理": ["view", "create", "edit", "delete", "payment"],
        "收入管理": ["view", "create", "edit", "delete", "review"],
        "成本核算": ["view", "calculate", "review", "adjust"],
        "财务报表": ["view", "export", "print", "config"]
    }'
END
ELSE
BEGIN
    -- 如果已有权限，在末尾添加财务权限
    SET @system_admin_permissions = REPLACE(@system_admin_permissions, '}', ',
        "财务管理": ["view", "create", "edit", "delete", "approve", "audit"],
        "会计科目管理": ["view", "create", "edit", "delete"],
        "财务凭证管理": ["view", "create", "edit", "delete", "review", "post"],
        "应付账款管理": ["view", "create", "edit", "delete", "payment"],
        "收入管理": ["view", "create", "edit", "delete", "review"],
        "成本核算": ["view", "calculate", "review", "adjust"],
        "财务报表": ["view", "export", "print", "config"]
    }')
END

UPDATE roles SET permissions = @system_admin_permissions WHERE name = '系统管理员'
PRINT '  ✓ 系统管理员权限更新完成'

-- =============================================
-- 更新超级管理员权限
-- =============================================

PRINT '更新超级管理员权限...'

DECLARE @super_admin_permissions NVARCHAR(MAX)
SELECT @super_admin_permissions = ISNULL(permissions, '{}') FROM roles WHERE name = '超级管理员'

-- 如果是空的JSON对象，直接设置完整权限
IF @super_admin_permissions = '{}' OR @super_admin_permissions IS NULL OR @super_admin_permissions = ''
BEGIN
    SET @super_admin_permissions = '{
        "财务管理": ["view", "create", "edit", "approve", "audit"],
        "会计科目管理": ["view"],
        "财务凭证管理": ["view", "create", "edit", "review"],
        "应付账款管理": ["view", "create", "edit", "payment"],
        "收入管理": ["view", "create", "edit", "review"],
        "成本核算": ["view", "calculate", "review"],
        "财务报表": ["view", "export", "print"]
    }'
END
ELSE
BEGIN
    -- 如果已有权限，在末尾添加财务权限
    SET @super_admin_permissions = REPLACE(@super_admin_permissions, '}', ',
        "财务管理": ["view", "create", "edit", "approve", "audit"],
        "会计科目管理": ["view"],
        "财务凭证管理": ["view", "create", "edit", "review"],
        "应付账款管理": ["view", "create", "edit", "payment"],
        "收入管理": ["view", "create", "edit", "review"],
        "成本核算": ["view", "calculate", "review"],
        "财务报表": ["view", "export", "print"]
    }')
END

UPDATE roles SET permissions = @super_admin_permissions WHERE name = '超级管理员'
PRINT '  ✓ 超级管理员权限更新完成'

-- =============================================
-- 更新学校管理员权限
-- =============================================

PRINT '更新学校管理员权限...'

DECLARE @school_admin_permissions NVARCHAR(MAX)
SELECT @school_admin_permissions = ISNULL(permissions, '{}') FROM roles WHERE name = '学校管理员'

-- 如果是空的JSON对象，直接设置完整权限
IF @school_admin_permissions = '{}' OR @school_admin_permissions IS NULL OR @school_admin_permissions = ''
BEGIN
    SET @school_admin_permissions = '{
        "财务管理": ["view", "approve"],
        "会计科目管理": ["view"],
        "财务凭证管理": ["view", "review"],
        "应付账款管理": ["view", "approve"],
        "收入管理": ["view", "review"],
        "成本核算": ["view", "review"],
        "财务报表": ["view", "export", "print"]
    }'
END
ELSE
BEGIN
    -- 如果已有权限，在末尾添加财务权限
    SET @school_admin_permissions = REPLACE(@school_admin_permissions, '}', ',
        "财务管理": ["view", "approve"],
        "会计科目管理": ["view"],
        "财务凭证管理": ["view", "review"],
        "应付账款管理": ["view", "approve"],
        "收入管理": ["view", "review"],
        "成本核算": ["view", "review"],
        "财务报表": ["view", "export", "print"]
    }')
END

UPDATE roles SET permissions = @school_admin_permissions WHERE name = '学校管理员'
PRINT '  ✓ 学校管理员权限更新完成'

-- =============================================
-- 创建财务专员角色
-- =============================================

PRINT '创建财务专员角色...'

IF NOT EXISTS (SELECT 1 FROM roles WHERE name = '财务专员')
BEGIN
    INSERT INTO roles (name, description, permissions, created_at)
    VALUES (
        '财务专员',
        '负责日常财务记账、凭证管理和基础财务报表',
        '{
            "财务管理": ["view", "create", "edit"],
            "会计科目管理": ["view"],
            "财务凭证管理": ["view", "create", "edit"],
            "应付账款管理": ["view", "create", "edit"],
            "收入管理": ["view", "create", "edit"],
            "成本核算": ["view", "calculate"],
            "财务报表": ["view", "export"],
            "库存管理": ["view"],
            "采购管理": ["view"]
        }',
        GETDATE()
    )
    PRINT '  ✓ 财务专员角色创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务专员角色已存在'
END

-- =============================================
-- 创建财务主管角色
-- =============================================

PRINT '创建财务主管角色...'

IF NOT EXISTS (SELECT 1 FROM roles WHERE name = '财务主管')
BEGIN
    INSERT INTO roles (name, description, permissions, created_at)
    VALUES (
        '财务主管',
        '负责财务审核、成本分析和高级财务报表',
        '{
            "财务管理": ["view", "create", "edit", "approve", "audit"],
            "会计科目管理": ["view", "create", "edit"],
            "财务凭证管理": ["view", "create", "edit", "review", "post"],
            "应付账款管理": ["view", "create", "edit", "payment", "approve"],
            "收入管理": ["view", "create", "edit", "review"],
            "成本核算": ["view", "calculate", "review", "adjust"],
            "财务报表": ["view", "export", "print", "config"],
            "库存管理": ["view", "approve"],
            "采购管理": ["view", "approve"]
        }',
        GETDATE()
    )
    PRINT '  ✓ 财务主管角色创建成功'
END
ELSE
BEGIN
    PRINT '  - 财务主管角色已存在'
END

-- =============================================
-- 创建出纳员角色
-- =============================================

PRINT '创建出纳员角色...'

IF NOT EXISTS (SELECT 1 FROM roles WHERE name = '出纳员')
BEGIN
    INSERT INTO roles (name, description, permissions, created_at)
    VALUES (
        '出纳员',
        '负责现金和银行存款管理、收付款记录',
        '{
            "财务管理": ["view"],
            "财务凭证管理": ["view"],
            "应付账款管理": ["view", "payment"],
            "收入管理": ["view", "create", "edit"],
            "成本核算": ["view"],
            "财务报表": ["view"],
            "库存管理": ["view"],
            "采购管理": ["view"]
        }',
        GETDATE()
    )
    PRINT '  ✓ 出纳员角色创建成功'
END
ELSE
BEGIN
    PRINT '  - 出纳员角色已存在'
END

-- =============================================
-- 更新食堂管理员权限
-- =============================================

PRINT '更新食堂管理员权限...'

DECLARE @cafeteria_admin_permissions NVARCHAR(MAX)
SELECT @cafeteria_admin_permissions = ISNULL(permissions, '{}') FROM roles WHERE name = '食堂管理员'

-- 如果是空的JSON对象，直接设置完整权限
IF @cafeteria_admin_permissions = '{}' OR @cafeteria_admin_permissions IS NULL OR @cafeteria_admin_permissions = ''
BEGIN
    SET @cafeteria_admin_permissions = '{
        "财务管理": ["view"],
        "成本核算": ["view"],
        "财务报表": ["view"]
    }'
END
ELSE
BEGIN
    -- 如果已有权限，在末尾添加财务权限
    SET @cafeteria_admin_permissions = REPLACE(@cafeteria_admin_permissions, '}', ',
        "财务管理": ["view"],
        "成本核算": ["view"],
        "财务报表": ["view"]
    }')
END

UPDATE roles SET permissions = @cafeteria_admin_permissions WHERE name = '食堂管理员'
PRINT '  ✓ 食堂管理员权限更新完成'

-- =============================================
-- 验证权限配置结果
-- =============================================

PRINT ''
PRINT '权限配置完成！验证结果：'
PRINT '========================================'

SELECT
    name AS '角色名称',
    description AS '角色描述',
    CASE
        WHEN permissions LIKE '%"财务管理"%'
        THEN '有财务权限'
        ELSE '无财务权限'
    END AS '财务权限状态',
    LEN(permissions) AS '权限JSON长度'
FROM roles
WHERE name IN ('系统管理员', '超级管理员', '学校管理员', '财务专员', '财务主管', '出纳员', '食堂管理员')
ORDER BY
    CASE name
        WHEN '系统管理员' THEN 1
        WHEN '超级管理员' THEN 2
        WHEN '财务主管' THEN 3
        WHEN '财务专员' THEN 4
        WHEN '出纳员' THEN 5
        WHEN '学校管理员' THEN 6
        WHEN '食堂管理员' THEN 7
        ELSE 8
    END

PRINT ''
PRINT '财务系统权限配置完成！'
PRINT '========================================'
