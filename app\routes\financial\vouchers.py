"""
财务凭证管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import FinancialVoucher, VoucherDetail, AccountingSubject
from app.forms.financial import FinancialVoucherForm
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text
from datetime import datetime, date
import json


@financial_bp.route('/vouchers')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def vouchers_index():
    """财务凭证列表"""
    user_area = current_user.get_current_area()
    
    # 获取搜索参数
    keyword = request.args.get('keyword', '').strip()
    voucher_type = request.args.get('voucher_type', '').strip()
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    
    # 构建查询
    query = FinancialVoucher.query.filter_by(area_id=user_area.id)
    
    if keyword:
        query = query.filter(
            db.or_(
                FinancialVoucher.voucher_number.like(f'%{keyword}%'),
                FinancialVoucher.summary.like(f'%{keyword}%')
            )
        )
    
    if voucher_type:
        query = query.filter_by(voucher_type=voucher_type)
    
    if status:
        query = query.filter_by(status=status)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(FinancialVoucher.voucher_date >= start_date_obj)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(FinancialVoucher.voucher_date <= end_date_obj)
        except ValueError:
            pass
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    vouchers = query.order_by(FinancialVoucher.voucher_date.desc(), 
                             FinancialVoucher.voucher_number.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    return render_template('financial/vouchers/index.html',
                         vouchers=vouchers,
                         keyword=keyword,
                         voucher_type=voucher_type,
                         status=status,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/vouchers/create', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'create')
def create_voucher():
    """创建财务凭证"""
    form = FinancialVoucherForm()
    user_area = current_user.get_current_area()
    
    if form.validate_on_submit():
        try:
            # 生成凭证号
            today = form.voucher_date.data
            prefix = f"PZ{today.strftime('%Y%m%d')}"
            
            # 查找当日最大凭证号
            last_voucher = FinancialVoucher.query.filter(
                FinancialVoucher.area_id == user_area.id,
                FinancialVoucher.voucher_number.like(f'{prefix}%')
            ).order_by(FinancialVoucher.voucher_number.desc()).first()
            
            if last_voucher:
                last_number = int(last_voucher.voucher_number[-3:])
                voucher_number = f"{prefix}{last_number + 1:03d}"
            else:
                voucher_number = f"{prefix}001"
            
            # 使用原始SQL创建凭证
            sql = text("""
                INSERT INTO financial_vouchers 
                (voucher_number, voucher_date, area_id, voucher_type, summary, 
                 total_amount, status, source_type, created_by, notes)
                OUTPUT inserted.id
                VALUES 
                (:voucher_number, :voucher_date, :area_id, :voucher_type, :summary,
                 :total_amount, :status, :source_type, :created_by, :notes)
            """)
            
            params = {
                'voucher_number': voucher_number,
                'voucher_date': form.voucher_date.data,
                'area_id': user_area.id,
                'voucher_type': form.voucher_type.data,
                'summary': form.summary.data,
                'total_amount': 0,  # 初始为0，后续通过明细计算
                'status': '草稿',
                'source_type': '手工录入',
                'created_by': current_user.id,
                'notes': form.notes.data
            }
            
            result = db.session.execute(sql, params)
            voucher_id = result.fetchone()[0]
            db.session.commit()
            
            flash('财务凭证创建成功', 'success')
            return redirect(url_for('financial.edit_voucher', id=voucher_id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"创建财务凭证失败: {str(e)}")
            flash('创建失败，请重试', 'danger')
    
    return render_template('financial/vouchers/form.html', form=form)


@financial_bp.route('/vouchers/<int:id>')
@login_required
@school_required
@check_permission('财务凭证管理', 'view')
def view_voucher(id):
    """查看财务凭证"""
    user_area = current_user.get_current_area()
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()
    
    return render_template('financial/vouchers/view.html', 
                         voucher=voucher, details=details)


@financial_bp.route('/vouchers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'edit')
def edit_voucher(id):
    """编辑财务凭证"""
    user_area = current_user.get_current_area()
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 已审核的凭证不允许编辑
    if voucher.status in ['已审核', '已记账']:
        flash('已审核的凭证不允许编辑', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))
    
    form = FinancialVoucherForm(obj=voucher)
    
    if form.validate_on_submit():
        try:
            # 使用原始SQL更新凭证
            sql = text("""
                UPDATE financial_vouchers
                SET voucher_date = :voucher_date,
                    voucher_type = :voucher_type,
                    summary = :summary,
                    notes = :notes
                WHERE id = :id AND area_id = :area_id
            """)
            
            params = {
                'voucher_date': form.voucher_date.data,
                'voucher_type': form.voucher_type.data,
                'summary': form.summary.data,
                'notes': form.notes.data,
                'id': id,
                'area_id': user_area.id
            }
            
            db.session.execute(sql, params)
            db.session.commit()
            
            flash('财务凭证更新成功', 'success')
            return redirect(url_for('financial.view_voucher', id=id))
            
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"更新财务凭证失败: {str(e)}")
            flash('更新失败，请重试', 'danger')
    
    # 获取凭证明细
    details = VoucherDetail.query.filter_by(voucher_id=id).order_by(VoucherDetail.line_number).all()
    
    return render_template('financial/vouchers/edit.html', 
                         form=form, voucher=voucher, details=details)


@financial_bp.route('/vouchers/<int:id>/delete', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'delete')
def delete_voucher(id):
    """删除财务凭证"""
    user_area = current_user.get_current_area()
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 已审核的凭证不允许删除
    if voucher.status in ['已审核', '已记账']:
        flash('已审核的凭证不允许删除', 'warning')
        return redirect(url_for('financial.vouchers_index'))
    
    try:
        # 使用原始SQL删除（级联删除明细）
        sql = text("DELETE FROM financial_vouchers WHERE id = :id AND area_id = :area_id")
        db.session.execute(sql, {'id': id, 'area_id': user_area.id})
        db.session.commit()
        
        flash('财务凭证删除成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除财务凭证失败: {str(e)}")
        flash('删除失败，请重试', 'danger')
    
    return redirect(url_for('financial.vouchers_index'))


@financial_bp.route('/vouchers/<int:id>/review', methods=['POST'])
@login_required
@school_required
@check_permission('财务凭证管理', 'review')
def review_voucher(id):
    """审核财务凭证"""
    user_area = current_user.get_current_area()
    voucher = FinancialVoucher.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    if voucher.status != '待审核':
        flash('只能审核待审核状态的凭证', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))
    
    # 检查凭证明细是否平衡
    details = VoucherDetail.query.filter_by(voucher_id=id).all()
    if not details:
        flash('凭证没有明细，无法审核', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))
    
    total_debit = sum(detail.debit_amount for detail in details)
    total_credit = sum(detail.credit_amount for detail in details)
    
    if abs(total_debit - total_credit) > 0.01:  # 允许0.01的误差
        flash('凭证借贷不平衡，无法审核', 'warning')
        return redirect(url_for('financial.view_voucher', id=id))
    
    try:
        # 使用原始SQL更新审核状态
        sql = text("""
            UPDATE financial_vouchers
            SET status = '已审核',
                reviewed_by = :reviewed_by,
                reviewed_at = GETDATE()
            WHERE id = :id AND area_id = :area_id
        """)
        
        db.session.execute(sql, {
            'reviewed_by': current_user.id,
            'id': id,
            'area_id': user_area.id
        })
        db.session.commit()
        
        flash('凭证审核成功', 'success')
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"审核凭证失败: {str(e)}")
        flash('审核失败，请重试', 'danger')
    
    return redirect(url_for('financial.view_voucher', id=id))
