#!/usr/bin/env python3
"""
财务系统模块可见性初始化脚本
为所有角色设置财务系统模块的默认可见性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import Role
from app.models_visibility import ModuleVisibility
from sqlalchemy import text

def init_financial_module_visibility():
    """初始化财务系统模块可见性设置"""
    app = create_app()
    
    with app.app_context():
        print("=== 财务系统模块可见性初始化 ===\n")
        
        # 财务系统的所有模块ID
        financial_modules = [
            'financial',              # 财务管理主模块
            'financial_overview',     # 财务概览
            'accounting_subjects',    # 会计科目
            'financial_vouchers',     # 财务凭证
            'account_payables',       # 应付账款
            'payment_records',        # 付款记录
            'pending_stock_ins',      # 待处理入库
            'balance_sheet',          # 资产负债表
            'income_statement',       # 利润表
            'payables_aging'          # 账龄分析
        ]
        
        # 获取所有角色
        roles = Role.query.all()
        if not roles:
            print("❌ 未找到任何角色，请先创建角色")
            return False
        
        print(f"📋 找到 {len(roles)} 个角色")
        for role in roles:
            print(f"   - {role.name} (ID: {role.id})")
        
        print(f"\n🔧 需要设置的财务模块: {len(financial_modules)} 个")
        for module in financial_modules:
            print(f"   - {module}")
        
        # 为每个角色设置财务模块的默认可见性
        total_settings = 0
        updated_settings = 0
        created_settings = 0
        
        for role in roles:
            print(f"\n🔄 处理角色: {role.name}")
            
            for module_id in financial_modules:
                try:
                    # 检查是否已存在设置
                    existing = ModuleVisibility.query.filter_by(
                        module_id=module_id,
                        role_id=role.id
                    ).first()
                    
                    if existing:
                        print(f"   ✓ {module_id}: 已存在设置 (可见性: {'是' if existing.is_visible else '否'})")
                        updated_settings += 1
                    else:
                        # 根据角色类型设置默认可见性
                        default_visibility = get_default_visibility(role, module_id)
                        
                        # 使用原始SQL创建记录
                        sql = text("""
                            INSERT INTO module_visibility (module_id, role_id, is_visible, created_at, updated_at)
                            VALUES (:module_id, :role_id, :is_visible, GETDATE(), GETDATE())
                        """)
                        
                        db.session.execute(sql, {
                            'module_id': module_id,
                            'role_id': role.id,
                            'is_visible': 1 if default_visibility else 0
                        })
                        
                        print(f"   ➕ {module_id}: 创建新设置 (可见性: {'是' if default_visibility else '否'})")
                        created_settings += 1
                    
                    total_settings += 1
                    
                except Exception as e:
                    print(f"   ❌ {module_id}: 设置失败 - {str(e)}")
        
        # 提交所有更改
        try:
            db.session.commit()
            print(f"\n✅ 财务模块可见性设置完成！")
            print(f"   📊 总计处理: {total_settings} 个设置")
            print(f"   ➕ 新创建: {created_settings} 个")
            print(f"   🔄 已存在: {updated_settings} 个")
            
            # 显示统计信息
            print(f"\n📈 统计信息:")
            for role in roles:
                visible_count = ModuleVisibility.query.filter_by(
                    role_id=role.id,
                    is_visible=1
                ).filter(ModuleVisibility.module_id.in_(financial_modules)).count()
                
                total_count = len(financial_modules)
                print(f"   {role.name}: {visible_count}/{total_count} 个财务模块可见")
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 提交失败: {str(e)}")
            return False

def get_default_visibility(role, module_id):
    """根据角色和模块ID获取默认可见性"""
    
    # 管理员角色默认可见所有模块
    if '管理员' in role.name or 'admin' in role.name.lower():
        return True
    
    # 财务相关角色默认可见所有财务模块
    if any(keyword in role.name for keyword in ['财务', '会计', '出纳']):
        return True
    
    # 校长、副校长等高级管理角色可见报表模块
    if any(keyword in role.name for keyword in ['校长', '副校长', '主任']):
        if module_id in ['financial', 'financial_overview', 'balance_sheet', 'income_statement', 'payables_aging']:
            return True
        return False
    
    # 采购相关角色可见应付账款相关模块
    if any(keyword in role.name for keyword in ['采购', '库管']):
        if module_id in ['financial', 'account_payables', 'payment_records', 'pending_stock_ins']:
            return True
        return False
    
    # 其他角色默认不可见财务模块
    return False

def check_financial_permissions():
    """检查财务权限是否正确配置"""
    print("\n🔍 检查财务权限配置...")
    
    try:
        from app.utils.permissions import PERMISSIONS
        
        financial_permission_modules = [
            '财务管理',
            '会计科目管理', 
            '财务凭证管理',
            '应付账款管理',
            '收入管理',
            '成本核算',
            '财务报表'
        ]
        
        missing_modules = []
        for module in financial_permission_modules:
            if module not in PERMISSIONS:
                missing_modules.append(module)
            else:
                actions = list(PERMISSIONS[module]['actions'].keys())
                print(f"   ✓ {module}: {len(actions)} 个权限操作")
        
        if missing_modules:
            print(f"   ❌ 缺少权限模块: {', '.join(missing_modules)}")
            return False
        else:
            print(f"   ✅ 所有财务权限模块已配置")
            return True
            
    except Exception as e:
        print(f"   ❌ 检查权限配置失败: {str(e)}")
        return False

def show_usage_guide():
    """显示使用指南"""
    print("\n📚 财务系统使用指南:")
    print("1. 访问后台管理 -> 系统 -> 模块可见性管理")
    print("2. 为不同角色调整财务模块的可见性")
    print("3. 访问后台管理 -> 系统 -> 角色权限管理")
    print("4. 为角色分配相应的财务权限")
    print("5. 用户登录后即可在导航菜单中看到财务管理模块")
    
    print("\n🔗 财务系统访问地址:")
    print("   - 财务管理首页: /financial/reports")
    print("   - 会计科目管理: /financial/accounting-subjects")
    print("   - 财务凭证管理: /financial/vouchers")
    print("   - 应付账款管理: /financial/payables")
    print("   - 付款记录管理: /financial/payments")

if __name__ == '__main__':
    print("财务系统模块可见性初始化工具")
    print("=" * 50)
    
    # 初始化模块可见性
    success = init_financial_module_visibility()
    
    if success:
        # 检查权限配置
        check_financial_permissions()
        
        # 显示使用指南
        show_usage_guide()
        
        print("\n🎉 财务系统模块可见性初始化完成！")
    else:
        print("\n❌ 初始化失败，请检查错误信息")
        sys.exit(1)
