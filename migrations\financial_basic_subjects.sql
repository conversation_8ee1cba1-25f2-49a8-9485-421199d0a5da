-- 财务系统基础会计科目初始化脚本
-- 为每个学校区域创建标准的会计科目体系

DECLARE @area_id INT;
DECLARE area_cursor CURSOR FOR 
    SELECT id FROM areas WHERE level = 3; -- 学校级别

OPEN area_cursor;
FETCH NEXT FROM area_cursor INTO @area_id;

WHILE @@FETCH_STATUS = 0
BEGIN
    -- 检查该区域是否已经初始化过会计科目
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND is_system = 1)
    BEGIN
        PRINT '为区域 ' + CAST(@area_id AS NVARCHAR(10)) + ' 初始化会计科目...';
        
        -- 1. 资产类科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        -- 一级科目
        ('1001', '库存现金', NULL, 1, '资产', '借方', @area_id, 1, 1, '学校食堂的现金资产'),
        ('1002', '银行存款', NULL, 1, '资产', '借方', @area_id, 1, 1, '学校食堂在银行的存款'),
        ('1121', '应收账款', NULL, 1, '资产', '借方', @area_id, 1, 1, '应收的各种款项'),
        ('1401', '库存商品', NULL, 1, '资产', '借方', @area_id, 1, 1, '已加工完成的食品'),
        ('1402', '原材料', NULL, 1, '资产', '借方', @area_id, 1, 1, '采购的各种食材原料'),
        ('1403', '低值易耗品', NULL, 1, '资产', '借方', @area_id, 1, 1, '餐具、厨具等易耗品'),
        ('1601', '固定资产', NULL, 1, '资产', '借方', @area_id, 1, 1, '厨房设备、餐桌椅等固定资产'),
        ('1602', '累计折旧', NULL, 1, '资产', '贷方', @area_id, 1, 1, '固定资产的累计折旧');

        -- 获取刚插入的一级科目ID，用于创建二级科目
        DECLARE @cash_id INT = (SELECT id FROM accounting_subjects WHERE code = '1001' AND area_id = @area_id);
        DECLARE @bank_id INT = (SELECT id FROM accounting_subjects WHERE code = '1002' AND area_id = @area_id);
        DECLARE @material_id INT = (SELECT id FROM accounting_subjects WHERE code = '1402' AND area_id = @area_id);
        
        -- 二级科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('100201', '基本户', @bank_id, 2, '资产', '借方', @area_id, 1, 1, '学校食堂基本银行账户'),
        ('100202', '专用户', @bank_id, 2, '资产', '借方', @area_id, 1, 1, '学校食堂专用银行账户'),
        ('140201', '蔬菜类', @material_id, 2, '资产', '借方', @area_id, 1, 1, '各种蔬菜原料'),
        ('140202', '肉类', @material_id, 2, '资产', '借方', @area_id, 1, 1, '各种肉类原料'),
        ('140203', '粮油类', @material_id, 2, '资产', '借方', @area_id, 1, 1, '米面油等主食原料'),
        ('140204', '调料类', @material_id, 2, '资产', '借方', @area_id, 1, 1, '各种调味料');

        -- 2. 负债类科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('2201', '应付账款', NULL, 1, '负债', '贷方', @area_id, 1, 1, '应付给供应商的款项'),
        ('2202', '应付职工薪酬', NULL, 1, '负债', '贷方', @area_id, 1, 1, '应付给员工的工资福利'),
        ('2203', '应交税费', NULL, 1, '负债', '贷方', @area_id, 1, 1, '应交纳的各种税费');

        -- 获取负债类一级科目ID
        DECLARE @payable_id INT = (SELECT id FROM accounting_subjects WHERE code = '2201' AND area_id = @area_id);
        DECLARE @salary_id INT = (SELECT id FROM accounting_subjects WHERE code = '2202' AND area_id = @area_id);
        
        -- 负债类二级科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('220101', '食材供应商', @payable_id, 2, '负债', '贷方', @area_id, 1, 1, '应付食材供应商款项'),
        ('220102', '设备供应商', @payable_id, 2, '负债', '贷方', @area_id, 1, 1, '应付设备供应商款项'),
        ('220201', '应付工资', @salary_id, 2, '负债', '贷方', @area_id, 1, 1, '应付员工工资'),
        ('220202', '应付福利费', @salary_id, 2, '负债', '贷方', @area_id, 1, 1, '应付员工福利费');

        -- 3. 所有者权益类科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('3001', '实收资本', NULL, 1, '所有者权益', '贷方', @area_id, 1, 1, '学校投入的资本'),
        ('3103', '本年利润', NULL, 1, '所有者权益', '贷方', @area_id, 1, 1, '本年度实现的利润'),
        ('3104', '利润分配', NULL, 1, '所有者权益', '贷方', @area_id, 1, 1, '利润的分配情况');

        -- 4. 收入类科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('6001', '主营业务收入', NULL, 1, '收入', '贷方', @area_id, 1, 1, '食堂主要经营收入'),
        ('6051', '其他业务收入', NULL, 1, '收入', '贷方', @area_id, 1, 1, '食堂其他经营收入');

        -- 获取收入类一级科目ID
        DECLARE @main_income_id INT = (SELECT id FROM accounting_subjects WHERE code = '6001' AND area_id = @area_id);
        
        -- 收入类二级科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('600101', '学生餐费收入', @main_income_id, 2, '收入', '贷方', @area_id, 1, 1, '学生用餐收入'),
        ('600102', '教师餐费收入', @main_income_id, 2, '收入', '贷方', @area_id, 1, 1, '教师用餐收入'),
        ('600103', '外来人员餐费收入', @main_income_id, 2, '收入', '贷方', @area_id, 1, 1, '外来人员用餐收入');

        -- 5. 费用类科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('6401', '主营业务成本', NULL, 1, '费用', '借方', @area_id, 1, 1, '食堂主要经营成本'),
        ('6402', '管理费用', NULL, 1, '费用', '借方', @area_id, 1, 1, '食堂管理费用'),
        ('6403', '销售费用', NULL, 1, '费用', '借方', @area_id, 1, 1, '食堂销售费用'),
        ('6711', '营业外支出', NULL, 1, '费用', '借方', @area_id, 1, 1, '营业外支出');

        -- 获取费用类一级科目ID
        DECLARE @main_cost_id INT = (SELECT id FROM accounting_subjects WHERE code = '6401' AND area_id = @area_id);
        DECLARE @admin_cost_id INT = (SELECT id FROM accounting_subjects WHERE code = '6402' AND area_id = @area_id);
        
        -- 费用类二级科目
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, is_active, description)
        VALUES 
        ('640101', '食材成本', @main_cost_id, 2, '费用', '借方', @area_id, 1, 1, '食材采购成本'),
        ('640102', '人工成本', @main_cost_id, 2, '费用', '借方', @area_id, 1, 1, '人工工资成本'),
        ('640103', '燃料动力', @main_cost_id, 2, '费用', '借方', @area_id, 1, 1, '水电气等燃料动力费'),
        ('640201', '办公费', @admin_cost_id, 2, '费用', '借方', @area_id, 1, 1, '办公用品费用'),
        ('640202', '差旅费', @admin_cost_id, 2, '费用', '借方', @area_id, 1, 1, '差旅费用'),
        ('640203', '折旧费', @admin_cost_id, 2, '费用', '借方', @area_id, 1, 1, '固定资产折旧费');

        PRINT '区域 ' + CAST(@area_id AS NVARCHAR(10)) + ' 会计科目初始化完成！';
    END
    ELSE
    BEGIN
        PRINT '区域 ' + CAST(@area_id AS NVARCHAR(10)) + ' 已存在会计科目，跳过初始化。';
    END
    
    FETCH NEXT FROM area_cursor INTO @area_id;
END

CLOSE area_cursor;
DEALLOCATE area_cursor;

PRINT '所有学校区域的会计科目初始化完成！';

-- 显示统计信息
SELECT 
    a.name AS '学校名称',
    COUNT(s.id) AS '科目数量'
FROM areas a
LEFT JOIN accounting_subjects s ON a.id = s.area_id AND s.is_system = 1
WHERE a.level = 3
GROUP BY a.id, a.name
ORDER BY a.name;
