{% extends "financial/base.html" %}

{% block page_title %}财务管理首页{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item active">财务管理</li>
{% endblock %}

{% block financial_content %}
<!-- 财务概览 -->
<div class="financial-summary-box">
    <h4><i class="fas fa-chart-line"></i> 财务概览</h4>
    <div class="row">
        <div class="col-md-3">
            <div class="summary-item">
                <div class="summary-label">本月收入</div>
                <div class="summary-value" id="monthly-income">¥0.00</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="summary-item">
                <div class="summary-label">本月支出</div>
                <div class="summary-value" id="monthly-expense">¥0.00</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="summary-item">
                <div class="summary-label">应付账款</div>
                <div class="summary-value" id="total-payables">¥0.00</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="summary-item">
                <div class="summary-label">本月利润</div>
                <div class="summary-value" id="monthly-profit">¥0.00</div>
            </div>
        </div>
    </div>
</div>

<!-- 功能模块 -->
<div class="row">
    <!-- 会计科目管理 -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="financial-card h-100">
            <div class="financial-card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-list-alt fa-3x text-primary"></i>
                </div>
                <h5 class="card-title">会计科目管理</h5>
                <p class="card-text text-muted">
                    管理会计科目体系，设置科目编码和分类
                </p>
                <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-primary">
                    <i class="fas fa-cog"></i> 管理科目
                </a>
            </div>
        </div>
    </div>
    
    <!-- 财务凭证管理 -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="financial-card h-100">
            <div class="financial-card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-file-invoice fa-3x text-success"></i>
                </div>
                <h5 class="card-title">财务凭证管理</h5>
                <p class="card-text text-muted">
                    创建和管理财务凭证，记录会计分录
                </p>
                <a href="{{ url_for('financial.vouchers_index') }}" class="btn btn-success">
                    <i class="fas fa-plus"></i> 管理凭证
                </a>
            </div>
        </div>
    </div>
    
    <!-- 应付账款管理 -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="financial-card h-100">
            <div class="financial-card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-credit-card fa-3x text-warning"></i>
                </div>
                <h5 class="card-title">应付账款管理</h5>
                <p class="card-text text-muted">
                    管理供应商应付账款，跟踪付款状态
                </p>
                <a href="{{ url_for('financial.payables_index') }}" class="btn btn-warning">
                    <i class="fas fa-money-bill"></i> 管理账款
                </a>
            </div>
        </div>
    </div>
    
    <!-- 付款记录管理 -->
    <div class="col-lg-3 col-md-6 mb-4">
        <div class="financial-card h-100">
            <div class="financial-card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-hand-holding-usd fa-3x text-info"></i>
                </div>
                <h5 class="card-title">付款记录管理</h5>
                <p class="card-text text-muted">
                    记录和管理所有付款交易记录
                </p>
                <a href="{{ url_for('financial.payments_index') }}" class="btn btn-info">
                    <i class="fas fa-list"></i> 查看付款
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 财务报表 -->
<div class="row">
    <div class="col-12">
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-chart-bar"></i> 财务报表
            </div>
            <div class="financial-card-body">
                <div class="row">
                    <!-- 资产负债表 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-left-primary h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            资产负债表
                                        </div>
                                        <div class="text-xs text-gray-800">
                                            反映特定日期的财务状况
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-balance-scale fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{{ url_for('financial.balance_sheet') }}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i> 查看报表
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 利润表 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-left-success h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            利润表
                                        </div>
                                        <div class="text-xs text-gray-800">
                                            反映特定期间的经营成果
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{{ url_for('financial.income_statement') }}" class="btn btn-success btn-sm">
                                        <i class="fas fa-eye"></i> 查看报表
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 应付账款账龄分析 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-left-warning h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            账龄分析
                                        </div>
                                        <div class="text-xs text-gray-800">
                                            分析应付账款的账龄结构
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{{ url_for('financial.payables_aging') }}" class="btn btn-warning btn-sm">
                                        <i class="fas fa-eye"></i> 查看分析
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <!-- 凭证汇总表 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-left-info h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            凭证汇总
                                        </div>
                                        <div class="text-xs text-gray-800">
                                            汇总分析财务凭证数据
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-file-alt fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{{ url_for('financial.voucher_summary') }}" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i> 查看汇总
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 待处理事项 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-left-danger h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            待处理事项
                                        </div>
                                        <div class="text-xs text-gray-800">
                                            需要处理的财务事项
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <a href="{{ url_for('financial.pending_stock_ins') }}" class="btn btn-danger btn-sm">
                                        <i class="fas fa-tasks"></i> 查看待办
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 数据导出 -->
                    <div class="col-lg-4 col-md-6 mb-3">
                        <div class="card border-left-secondary h-100">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-secondary text-uppercase mb-1">
                                            数据导出
                                        </div>
                                        <div class="text-xs text-gray-800">
                                            导出财务数据和报表
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-download fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                                <div class="mt-2">
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('financial.export_report', report_type='payables') }}" 
                                           class="btn btn-secondary">应付账款</a>
                                        <a href="{{ url_for('financial.export_report', report_type='payments') }}" 
                                           class="btn btn-secondary">付款记录</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row">
    <div class="col-12">
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-bolt"></i> 快速操作
            </div>
            <div class="financial-card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('financial.create_voucher') }}" class="btn btn-outline-primary btn-block">
                            <i class="fas fa-plus"></i> 新建凭证
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('financial.create_payment') }}" class="btn btn-outline-success btn-block">
                            <i class="fas fa-money-bill"></i> 记录付款
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('financial.create_accounting_subject') }}" class="btn btn-outline-info btn-block">
                            <i class="fas fa-plus"></i> 新增科目
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('financial.pending_stock_ins') }}" class="btn btn-outline-warning btn-block">
                            <i class="fas fa-tasks"></i> 处理入库
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 财务首页特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // 加载财务概览数据
    loadFinancialSummary();
    
    // 每5分钟刷新一次数据
    setInterval(loadFinancialSummary, 5 * 60 * 1000);
});

function loadFinancialSummary() {
    // 这里可以通过AJAX加载实际的财务数据
    // 暂时使用模拟数据
    const summaryData = {
        monthlyIncome: 125680.50,
        monthlyExpense: 98750.30,
        totalPayables: 45230.80,
        monthlyProfit: 26930.20
    };
    
    // 更新显示
    document.getElementById('monthly-income').textContent = formatAmountChinese(summaryData.monthlyIncome);
    document.getElementById('monthly-expense').textContent = formatAmountChinese(summaryData.monthlyExpense);
    document.getElementById('total-payables').textContent = formatAmountChinese(summaryData.totalPayables);
    document.getElementById('monthly-profit').textContent = formatAmountChinese(summaryData.monthlyProfit);
    
    // 设置利润颜色
    const profitElement = document.getElementById('monthly-profit');
    if (summaryData.monthlyProfit > 0) {
        profitElement.style.color = '#1cc88a';
    } else if (summaryData.monthlyProfit < 0) {
        profitElement.style.color = '#e74a3b';
    } else {
        profitElement.style.color = '#858796';
    }
}
</script>
{% endblock %}
