{% extends "financial/base.html" %}

{% block page_title %}
{% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="{{ url_for('financial.accounting_subjects_index') }}">会计科目管理</a></li>
<li class="breadcrumb-item active">
    {% if subject %}编辑科目{% else %}新增科目{% endif %}
</li>
{% endblock %}

{% block page_actions %}
<div class="financial-actions">
    <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> 返回列表
    </a>
</div>
{% endblock %}

{% block financial_content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-{% if subject %}edit{% else %}plus{% endif %}"></i>
                {% if subject %}编辑会计科目{% else %}新增会计科目{% endif %}
            </div>
            <div class="financial-card-body">
                <form method="POST" class="financial-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.code.label(class="form-label") }}
                                {{ form.code(class="form-control" + (" is-invalid" if form.code.errors else "")) }}
                                {% if form.code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.code.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted">
                                    科目编码用于唯一标识会计科目，建议使用数字编码
                                </small>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.subject_type.label(class="form-label") }}
                                {{ form.subject_type(class="form-control" + (" is-invalid" if form.subject_type.errors else ""), id="subject_type") }}
                                {% if form.subject_type.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.subject_type.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.balance_direction.label(class="form-label") }}
                                {{ form.balance_direction(class="form-control" + (" is-invalid" if form.balance_direction.errors else ""), id="balance_direction") }}
                                {% if form.balance_direction.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.balance_direction.errors %}
                                    <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text text-muted" id="balance_direction_help">
                                    余额方向将根据科目类型自动设置
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.parent_id.label(class="form-label") }}
                        {{ form.parent_id(class="form-control" + (" is-invalid" if form.parent_id.errors else "")) }}
                        {% if form.parent_id.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.parent_id.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            选择上级科目可以建立科目层级关系，便于分类管理
                        </small>
                    </div>
                    
                    <div class="form-group">
                        {{ form.description.label(class="form-label") }}
                        {{ form.description(class="form-control" + (" is-invalid" if form.description.errors else ""), rows="3") }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.description.errors %}
                            <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            可选，用于说明科目的用途和核算内容
                        </small>
                    </div>
                    
                    <div class="form-group text-center">
                        {{ form.submit(class="btn btn-primary btn-lg") }}
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="btn btn-secondary btn-lg">取消</a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 科目编码建议 -->
        <div class="financial-card">
            <div class="financial-card-header">
                <i class="fas fa-lightbulb"></i> 科目编码建议
            </div>
            <div class="financial-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>资产类科目 (1xxx)</h6>
                        <ul class="small">
                            <li>1001 - 库存现金</li>
                            <li>1002 - 银行存款</li>
                            <li>1121 - 应收账款</li>
                            <li>1401 - 库存商品</li>
                            <li>1402 - 原材料</li>
                            <li>1601 - 固定资产</li>
                        </ul>
                        
                        <h6>负债类科目 (2xxx)</h6>
                        <ul class="small">
                            <li>2201 - 应付账款</li>
                            <li>2202 - 应付职工薪酬</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>所有者权益类科目 (3xxx)</h6>
                        <ul class="small">
                            <li>3001 - 实收资本</li>
                            <li>3103 - 本年利润</li>
                        </ul>
                        
                        <h6>收入类科目 (6xxx)</h6>
                        <ul class="small">
                            <li>6001 - 主营业务收入</li>
                            <li>6001001 - 学生餐费收入</li>
                            <li>6001002 - 教师餐费收入</li>
                        </ul>
                        
                        <h6>费用类科目 (6xxx)</h6>
                        <ul class="small">
                            <li>6401 - 主营业务成本</li>
                            <li>6402 - 管理费用</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block financial_js %}
<script>
// 会计科目表单页面特定JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const subjectTypeSelect = document.getElementById('subject_type');
    const balanceDirectionSelect = document.getElementById('balance_direction');
    const balanceDirectionHelp = document.getElementById('balance_direction_help');
    
    // 科目类型与余额方向的对应关系
    const balanceDirectionMap = {
        '资产': '借方',
        '费用': '借方',
        '负债': '贷方',
        '所有者权益': '贷方',
        '收入': '贷方'
    };
    
    // 科目类型说明
    const subjectTypeDescriptions = {
        '资产': '资产类科目余额方向为借方，用于核算学校食堂拥有的各种资产',
        '负债': '负债类科目余额方向为贷方，用于核算学校食堂的各种债务',
        '所有者权益': '所有者权益类科目余额方向为贷方，用于核算学校食堂的净资产',
        '收入': '收入类科目余额方向为贷方，用于核算学校食堂的各种收入',
        '费用': '费用类科目余额方向为借方，用于核算学校食堂的各种支出'
    };
    
    // 科目类型变化时自动设置余额方向
    function updateBalanceDirection() {
        const selectedType = subjectTypeSelect.value;
        if (selectedType && balanceDirectionMap[selectedType]) {
            balanceDirectionSelect.value = balanceDirectionMap[selectedType];
            balanceDirectionHelp.textContent = subjectTypeDescriptions[selectedType];
        } else {
            balanceDirectionHelp.textContent = '余额方向将根据科目类型自动设置';
        }
    }
    
    // 监听科目类型变化
    subjectTypeSelect.addEventListener('change', updateBalanceDirection);
    
    // 页面加载时初始化
    updateBalanceDirection();
    
    // 表单验证
    const form = document.querySelector('.financial-form');
    form.addEventListener('submit', function(e) {
        const code = document.getElementById('code').value.trim();
        const name = document.getElementById('name').value.trim();
        
        if (!code) {
            alert('请输入科目编码');
            e.preventDefault();
            return false;
        }
        
        if (!name) {
            alert('请输入科目名称');
            e.preventDefault();
            return false;
        }
        
        // 检查科目编码格式（建议为数字）
        if (!/^\d+$/.test(code)) {
            if (!confirm('科目编码建议使用纯数字格式，确定要使用当前编码吗？')) {
                e.preventDefault();
                return false;
            }
        }
        
        // 显示提交状态
        const submitBtn = form.querySelector('input[type="submit"]');
        showLoading(submitBtn);
    });
});
</script>
{% endblock %}
