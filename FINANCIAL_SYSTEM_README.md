# 学校食堂财务管理系统

## 📋 系统概述

学校食堂财务管理系统是一个完整的财务管理解决方案，专为学校食堂的财务管理需求而设计。系统提供了从基础会计科目设置到完整财务报表的全流程管理功能。

## 🏗️ 系统架构

### 核心模块
- **会计科目管理**: 标准会计科目体系，支持多级科目结构
- **财务凭证管理**: 完整的凭证创建、审核、记账流程
- **应付账款管理**: 与入库单集成的应付账款管理
- **付款记录管理**: 付款记录跟踪和凭证生成
- **财务报表**: 资产负债表、利润表、账龄分析等

### 技术特点
- 🔒 学校级数据隔离
- 🎯 严格的权限控制
- 📊 实时数据更新
- 🔄 与现有系统无缝集成
- 📱 响应式界面设计

## 🚀 部署指南

### 1. 数据库初始化

#### 步骤1: 创建财务系统表结构
```sql
-- 在SQL Server中执行
sqlcmd -S [服务器名] -d [数据库名] -i migrations/financial_system_init.sql
```

#### 步骤2: 初始化基础会计科目
```sql
-- 为所有学校创建标准会计科目
sqlcmd -S [服务器名] -d [数据库名] -i migrations/financial_basic_subjects.sql
```

### 2. 系统验证

运行测试脚本验证系统是否正常：
```bash
python test_financial_system.py
```

### 3. 权限配置

确保用户具有相应的财务管理权限：
- `财务管理` - 基础财务权限
- `会计科目管理` - 科目设置权限
- `财务凭证管理` - 凭证操作权限
- `应付账款管理` - 账款管理权限
- `财务报表` - 报表查看权限

## 📚 使用指南

### 首次使用流程

1. **访问财务管理首页**
   - URL: `/financial/reports`
   - 查看财务概览和快速操作

2. **设置会计科目**
   - 访问: `/financial/accounting-subjects`
   - 系统已预设标准科目，可根据需要添加自定义科目

3. **处理入库单**
   - 访问: `/financial/pending-stock-ins`
   - 将已确认的入库单生成应付账款

4. **创建财务凭证**
   - 访问: `/financial/vouchers`
   - 系统可自动生成入库和付款凭证

5. **管理应付账款**
   - 访问: `/financial/payables`
   - 跟踪供应商应付款项

6. **记录付款**
   - 访问: `/financial/payments`
   - 记录付款并自动更新应付账款

7. **查看财务报表**
   - 资产负债表: `/financial/balance-sheet`
   - 利润表: `/financial/income-statement`
   - 账龄分析: `/financial/payables-aging`

### 业务流程

#### 采购到付款流程
```
采购订单 → 入库确认 → 财务确认 → 生成应付账款 → 付款记录 → 更新账款状态
```

#### 凭证处理流程
```
创建凭证 → 录入明细 → 借贷平衡检查 → 提交审核 → 审核通过 → 记账
```

## 🔧 配置说明

### 会计科目体系

系统预设了完整的会计科目体系：

#### 资产类 (1xxx)
- 1001 库存现金
- 1002 银行存款
- 1402 原材料
- 1601 固定资产

#### 负债类 (2xxx)
- 2201 应付账款
- 2202 应付职工薪酬

#### 所有者权益类 (3xxx)
- 3001 实收资本
- 3103 本年利润

#### 收入类 (6xxx)
- 6001 主营业务收入
  - 600101 学生餐费收入
  - 600102 教师餐费收入

#### 费用类 (6xxx)
- 6401 主营业务成本
- 6402 管理费用

### 权限配置

财务系统包含以下权限模块：

```python
'财务管理': ['view', 'create', 'edit', 'delete', 'approve', 'audit']
'会计科目管理': ['view', 'create', 'edit', 'delete']
'财务凭证管理': ['view', 'create', 'edit', 'delete', 'review', 'post']
'应付账款管理': ['view', 'create', 'edit', 'delete', 'payment', 'approve']
'财务报表': ['view', 'export', 'print', 'config']
```

## 🔍 故障排除

### 常见问题

1. **表不存在错误**
   - 确保已执行 `financial_system_init.sql`
   - 检查数据库连接和权限

2. **会计科目为空**
   - 执行 `financial_basic_subjects.sql` 初始化科目
   - 确保学校区域已创建

3. **权限不足**
   - 检查用户是否有相应的财务权限
   - 确认用户所属学校区域

4. **凭证不平衡**
   - 检查借贷金额是否相等
   - 确认会计科目设置正确

### 日志查看

系统错误日志位置：
- 应用日志: `logs/app.log`
- 数据库日志: SQL Server 错误日志

## 📊 数据备份

### 重要数据表
- `accounting_subjects` - 会计科目
- `financial_vouchers` - 财务凭证
- `voucher_details` - 凭证明细
- `account_payables` - 应付账款
- `payment_records` - 付款记录

### 备份建议
```sql
-- 备份财务相关表
BACKUP DATABASE [数据库名] TO DISK = 'backup_path'
WITH FORMAT, INIT, COMPRESSION
```

## 🔄 系统集成

### 与现有模块的集成

1. **入库管理系统**
   - 自动从入库单生成应付账款
   - 财务确认流程

2. **供应商管理系统**
   - 应付账款关联供应商信息
   - 付款记录跟踪

3. **用户权限系统**
   - 基于角色的权限控制
   - 学校级数据隔离

## 📞 技术支持

如遇到问题，请提供以下信息：
1. 错误信息截图
2. 操作步骤描述
3. 用户权限信息
4. 系统日志相关内容

## 📈 后续开发

计划中的功能扩展：
- 成本核算模块
- 预算管理
- 现金流量表
- 财务分析报告
- 移动端支持

---

**版本**: 1.0.0  
**更新日期**: 2024年12月  
**兼容性**: SQL Server 2016+, Python 3.8+
