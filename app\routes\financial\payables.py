"""
应付账款管理路由
"""

from flask import render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app import db
from app.routes.financial import financial_bp
from app.models_financial import AccountPayable, FinancialVoucher, VoucherDetail, AccountingSubject
from app.models import StockIn, Supplier
from app.utils.school_required import school_required
from app.utils.permissions import check_permission
from sqlalchemy import text
from datetime import datetime, date


@financial_bp.route('/payables')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payables_index():
    """应付账款列表"""
    user_area = current_user.get_current_area()
    
    # 获取搜索参数
    keyword = request.args.get('keyword', '').strip()
    supplier_id = request.args.get('supplier_id', type=int)
    status = request.args.get('status', '').strip()
    start_date = request.args.get('start_date', '').strip()
    end_date = request.args.get('end_date', '').strip()
    
    # 构建查询
    query = AccountPayable.query.filter_by(area_id=user_area.id)
    
    if keyword:
        query = query.filter(
            db.or_(
                AccountPayable.payable_number.like(f'%{keyword}%'),
                AccountPayable.invoice_number.like(f'%{keyword}%')
            )
        )
    
    if supplier_id:
        query = query.filter_by(supplier_id=supplier_id)
    
    if status:
        query = query.filter_by(status=status)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            query = query.filter(AccountPayable.created_at >= start_date_obj)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            query = query.filter(AccountPayable.created_at <= end_date_obj)
        except ValueError:
            pass
    
    # 分页
    page = request.args.get('page', 1, type=int)
    per_page = current_app.config.get('ITEMS_PER_PAGE', 20)
    
    payables = query.order_by(AccountPayable.created_at.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )
    
    # 获取供应商列表用于筛选
    suppliers = Supplier.query.join(
        db.session.query(AccountPayable.supplier_id).filter_by(area_id=user_area.id).distinct().subquery(),
        Supplier.id == db.text('supplier_id')
    ).all()
    
    return render_template('financial/payables/index.html',
                         payables=payables,
                         suppliers=suppliers,
                         keyword=keyword,
                         supplier_id=supplier_id,
                         status=status,
                         start_date=start_date,
                         end_date=end_date)


@financial_bp.route('/payables/<int:id>')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def view_payable(id):
    """查看应付账款详情"""
    user_area = current_user.get_current_area()
    payable = AccountPayable.query.filter_by(
        id=id, 
        area_id=user_area.id
    ).first_or_404()
    
    # 获取付款记录
    from app.models_financial import PaymentRecord
    payments = PaymentRecord.query.filter_by(payable_id=id).order_by(PaymentRecord.payment_date.desc()).all()
    
    return render_template('financial/payables/view.html', 
                         payable=payable, payments=payments)


@financial_bp.route('/payables/generate-from-stock-in', methods=['POST'])
@login_required
@school_required
@check_permission('应付账款管理', 'create')
def generate_payable_from_stock_in():
    """从入库单生成应付账款"""
    user_area = current_user.get_current_area()
    stock_in_id = request.json.get('stock_in_id')
    
    if not stock_in_id:
        return jsonify({'success': False, 'message': '请选择入库单'})
    
    # 检查入库单是否存在且属于当前学校
    stock_in = StockIn.query.filter_by(
        id=stock_in_id,
        area_id=user_area.id
    ).first()
    
    if not stock_in:
        return jsonify({'success': False, 'message': '入库单不存在'})
    
    # 检查是否已生成应付账款
    existing = AccountPayable.query.filter_by(stock_in_id=stock_in_id).first()
    if existing:
        return jsonify({'success': False, 'message': '该入库单已生成应付账款'})
    
    # 检查入库单是否已财务确认
    if not stock_in.is_financial_confirmed:
        return jsonify({'success': False, 'message': '入库单尚未财务确认'})
    
    try:
        # 生成应付账款编号
        today = date.today()
        prefix = f"AP{today.strftime('%Y%m%d')}"
        
        last_payable = AccountPayable.query.filter(
            AccountPayable.area_id == user_area.id,
            AccountPayable.payable_number.like(f'{prefix}%')
        ).order_by(AccountPayable.payable_number.desc()).first()
        
        if last_payable:
            last_number = int(last_payable.payable_number[-3:])
            payable_number = f"{prefix}{last_number + 1:03d}"
        else:
            payable_number = f"{prefix}001"
        
        # 使用原始SQL创建应付账款
        sql = text("""
            INSERT INTO account_payables 
            (payable_number, area_id, supplier_id, stock_in_id, purchase_order_id,
             original_amount, balance_amount, status, created_by)
            OUTPUT inserted.id
            VALUES 
            (:payable_number, :area_id, :supplier_id, :stock_in_id, :purchase_order_id,
             :original_amount, :balance_amount, :status, :created_by)
        """)
        
        params = {
            'payable_number': payable_number,
            'area_id': user_area.id,
            'supplier_id': stock_in.supplier_id,
            'stock_in_id': stock_in_id,
            'purchase_order_id': stock_in.purchase_order_id,
            'original_amount': stock_in.total_cost,
            'balance_amount': stock_in.total_cost,
            'status': '未付款',
            'created_by': current_user.id
        }
        
        result = db.session.execute(sql, params)
        payable_id = result.fetchone()[0]
        
        # 生成财务凭证
        voucher_sql = text("""
            INSERT INTO financial_vouchers 
            (voucher_number, voucher_date, area_id, voucher_type, summary,
             total_amount, status, source_type, source_id, created_by)
            OUTPUT inserted.id
            VALUES 
            (:voucher_number, :voucher_date, :area_id, :voucher_type, :summary,
             :total_amount, :status, :source_type, :source_id, :created_by)
        """)
        
        # 生成凭证号
        voucher_prefix = f"PZ{today.strftime('%Y%m%d')}"
        last_voucher = FinancialVoucher.query.filter(
            FinancialVoucher.area_id == user_area.id,
            FinancialVoucher.voucher_number.like(f'{voucher_prefix}%')
        ).order_by(FinancialVoucher.voucher_number.desc()).first()
        
        if last_voucher:
            last_number = int(last_voucher.voucher_number[-3:])
            voucher_number = f"{voucher_prefix}{last_number + 1:03d}"
        else:
            voucher_number = f"{voucher_prefix}001"
        
        voucher_params = {
            'voucher_number': voucher_number,
            'voucher_date': today,
            'area_id': user_area.id,
            'voucher_type': '入库凭证',
            'summary': f'入库单{stock_in.stock_in_number}生成应付账款',
            'total_amount': stock_in.total_cost,
            'status': '已审核',
            'source_type': '入库单',
            'source_id': stock_in_id,
            'created_by': current_user.id
        }
        
        voucher_result = db.session.execute(voucher_sql, voucher_params)
        voucher_id = voucher_result.fetchone()[0]
        
        # 获取会计科目
        inventory_subject = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            code='1402'  # 原材料
        ).first()
        
        payable_subject = AccountingSubject.query.filter_by(
            area_id=user_area.id,
            code='2201'  # 应付账款
        ).first()
        
        if inventory_subject and payable_subject:
            # 生成凭证明细
            detail_sql = text("""
                INSERT INTO voucher_details 
                (voucher_id, line_number, subject_id, summary, debit_amount, credit_amount)
                VALUES 
                (:voucher_id, :line_number, :subject_id, :summary, :debit_amount, :credit_amount)
            """)
            
            # 借：原材料
            db.session.execute(detail_sql, {
                'voucher_id': voucher_id,
                'line_number': 1,
                'subject_id': inventory_subject.id,
                'summary': f'入库单{stock_in.stock_in_number}',
                'debit_amount': stock_in.total_cost,
                'credit_amount': 0
            })
            
            # 贷：应付账款
            db.session.execute(detail_sql, {
                'voucher_id': voucher_id,
                'line_number': 2,
                'subject_id': payable_subject.id,
                'summary': f'入库单{stock_in.stock_in_number}',
                'debit_amount': 0,
                'credit_amount': stock_in.total_cost
            })
        
        # 更新入库单关联信息
        update_stock_in_sql = text("""
            UPDATE stock_ins 
            SET payable_id = :payable_id, voucher_id = :voucher_id
            WHERE id = :stock_in_id AND area_id = :area_id
        """)
        
        db.session.execute(update_stock_in_sql, {
            'payable_id': payable_id,
            'voucher_id': voucher_id,
            'stock_in_id': stock_in_id,
            'area_id': user_area.id
        })
        
        db.session.commit()
        
        return jsonify({
            'success': True, 
            'message': '应付账款生成成功',
            'payable_id': payable_id
        })
        
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"生成应付账款失败: {str(e)}")
        return jsonify({'success': False, 'message': '生成失败，请重试'})


@financial_bp.route('/payables/pending-stock-ins')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def pending_stock_ins():
    """待生成应付账款的入库单"""
    user_area = current_user.get_current_area()
    
    # 查询已财务确认但未生成应付账款的入库单
    stock_ins = StockIn.query.filter(
        StockIn.area_id == user_area.id,
        StockIn.is_financial_confirmed == True,
        StockIn.payable_id.is_(None)
    ).order_by(StockIn.created_at.desc()).all()
    
    return render_template('financial/payables/pending_stock_ins.html', stock_ins=stock_ins)


@financial_bp.route('/payables/api/summary')
@login_required
@school_required
@check_permission('应付账款管理', 'view')
def payables_summary_api():
    """应付账款汇总API"""
    user_area = current_user.get_current_area()

    # 统计各状态的应付账款
    summary = db.session.query(
        AccountPayable.status,
        db.func.count(AccountPayable.id).label('count'),
        db.func.sum(AccountPayable.balance_amount).label('total_amount')
    ).filter_by(area_id=user_area.id).group_by(AccountPayable.status).all()

    result = {}
    for status, count, total_amount in summary:
        result[status] = {
            'count': count,
            'total_amount': float(total_amount) if total_amount else 0
        }

    return jsonify(result)
