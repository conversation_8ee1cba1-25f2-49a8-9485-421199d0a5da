-- 财务系统初始化SQL脚本
-- 创建财务系统相关表和初始化基础数据

-- 1. 创建会计科目表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='accounting_subjects' AND xtype='U')
BEGIN
    CREATE TABLE accounting_subjects (
        id INT IDENTITY(1,1) PRIMARY KEY,
        code NVARCHAR(20) NOT NULL,
        name NVARCHAR(100) NOT NULL,
        parent_id INT NULL,
        level INT NOT NULL DEFAULT 1,
        subject_type NVARCHAR(20) NOT NULL,
        balance_direction NVARCHAR(10) NOT NULL,
        area_id INT NOT NULL,
        is_system BIT NOT NULL DEFAULT 0,
        is_active BIT NOT NULL DEFAULT 1,
        description NVARCHAR(500) NULL,
        created_by INT NULL,
        created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT FK_accounting_subjects_parent FOREIGN KEY (parent_id) REFERENCES accounting_subjects(id),
        CONSTRAINT FK_accounting_subjects_area FOREIGN KEY (area_id) REFERENCES areas(id),
        CONSTRAINT FK_accounting_subjects_creator FOREIGN KEY (created_by) REFERENCES users(id),
        CONSTRAINT UQ_accounting_subjects_code_area UNIQUE (code, area_id)
    );
    
    CREATE INDEX IX_accounting_subjects_area_type ON accounting_subjects(area_id, subject_type);
    CREATE INDEX IX_accounting_subjects_parent ON accounting_subjects(parent_id);
    CREATE INDEX IX_accounting_subjects_code ON accounting_subjects(code);
END

-- 2. 创建财务凭证表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='financial_vouchers' AND xtype='U')
BEGIN
    CREATE TABLE financial_vouchers (
        id INT IDENTITY(1,1) PRIMARY KEY,
        voucher_number NVARCHAR(50) NOT NULL,
        voucher_date DATE NOT NULL,
        area_id INT NOT NULL,
        voucher_type NVARCHAR(20) NOT NULL,
        summary NVARCHAR(200) NOT NULL,
        total_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        status NVARCHAR(20) NOT NULL DEFAULT '草稿',
        source_type NVARCHAR(20) NULL,
        source_id INT NULL,
        created_by INT NOT NULL,
        reviewed_by INT NULL,
        reviewed_at DATETIME2(1) NULL,
        posted_by INT NULL,
        posted_at DATETIME2(1) NULL,
        notes NVARCHAR(500) NULL,
        created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT FK_financial_vouchers_area FOREIGN KEY (area_id) REFERENCES areas(id),
        CONSTRAINT FK_financial_vouchers_creator FOREIGN KEY (created_by) REFERENCES users(id),
        CONSTRAINT FK_financial_vouchers_reviewer FOREIGN KEY (reviewed_by) REFERENCES users(id),
        CONSTRAINT FK_financial_vouchers_poster FOREIGN KEY (posted_by) REFERENCES users(id),
        CONSTRAINT UQ_financial_vouchers_number_area UNIQUE (voucher_number, area_id)
    );
    
    CREATE INDEX IX_financial_vouchers_area_date ON financial_vouchers(area_id, voucher_date);
    CREATE INDEX IX_financial_vouchers_status ON financial_vouchers(status);
    CREATE INDEX IX_financial_vouchers_type ON financial_vouchers(voucher_type);
END

-- 3. 创建凭证明细表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='voucher_details' AND xtype='U')
BEGIN
    CREATE TABLE voucher_details (
        id INT IDENTITY(1,1) PRIMARY KEY,
        voucher_id INT NOT NULL,
        line_number INT NOT NULL,
        subject_id INT NOT NULL,
        summary NVARCHAR(200) NOT NULL,
        debit_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        credit_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT FK_voucher_details_voucher FOREIGN KEY (voucher_id) REFERENCES financial_vouchers(id) ON DELETE CASCADE,
        CONSTRAINT FK_voucher_details_subject FOREIGN KEY (subject_id) REFERENCES accounting_subjects(id),
        CONSTRAINT UQ_voucher_details_voucher_line UNIQUE (voucher_id, line_number)
    );
    
    CREATE INDEX IX_voucher_details_voucher ON voucher_details(voucher_id);
    CREATE INDEX IX_voucher_details_subject ON voucher_details(subject_id);
END

-- 4. 创建应付账款表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='account_payables' AND xtype='U')
BEGIN
    CREATE TABLE account_payables (
        id INT IDENTITY(1,1) PRIMARY KEY,
        payable_number NVARCHAR(50) NOT NULL,
        area_id INT NOT NULL,
        supplier_id INT NOT NULL,
        stock_in_id INT NULL,
        purchase_order_id INT NULL,
        invoice_number NVARCHAR(50) NULL,
        invoice_date DATE NULL,
        original_amount DECIMAL(15,2) NOT NULL,
        paid_amount DECIMAL(15,2) NOT NULL DEFAULT 0,
        balance_amount DECIMAL(15,2) NOT NULL,
        status NVARCHAR(20) NOT NULL DEFAULT '未付款',
        due_date DATE NULL,
        created_by INT NOT NULL,
        notes NVARCHAR(500) NULL,
        created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT FK_account_payables_area FOREIGN KEY (area_id) REFERENCES areas(id),
        CONSTRAINT FK_account_payables_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
        CONSTRAINT FK_account_payables_stock_in FOREIGN KEY (stock_in_id) REFERENCES stock_ins(id),
        CONSTRAINT FK_account_payables_purchase_order FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id),
        CONSTRAINT FK_account_payables_creator FOREIGN KEY (created_by) REFERENCES users(id),
        CONSTRAINT UQ_account_payables_number_area UNIQUE (payable_number, area_id)
    );
    
    CREATE INDEX IX_account_payables_area_status ON account_payables(area_id, status);
    CREATE INDEX IX_account_payables_supplier ON account_payables(supplier_id);
    CREATE INDEX IX_account_payables_stock_in ON account_payables(stock_in_id);
END

-- 5. 创建付款记录表
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='payment_records' AND xtype='U')
BEGIN
    CREATE TABLE payment_records (
        id INT IDENTITY(1,1) PRIMARY KEY,
        payment_number NVARCHAR(50) NOT NULL,
        area_id INT NOT NULL,
        payment_date DATE NOT NULL,
        amount DECIMAL(15,2) NOT NULL,
        payment_method NVARCHAR(20) NOT NULL,
        payable_id INT NOT NULL,
        supplier_id INT NOT NULL,
        voucher_id INT NULL,
        bank_account NVARCHAR(100) NULL,
        reference_number NVARCHAR(50) NULL,
        summary NVARCHAR(200) NOT NULL,
        status NVARCHAR(20) NOT NULL DEFAULT '已确认',
        created_by INT NOT NULL,
        notes NVARCHAR(500) NULL,
        created_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE(),
        
        CONSTRAINT FK_payment_records_area FOREIGN KEY (area_id) REFERENCES areas(id),
        CONSTRAINT FK_payment_records_payable FOREIGN KEY (payable_id) REFERENCES account_payables(id),
        CONSTRAINT FK_payment_records_supplier FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
        CONSTRAINT FK_payment_records_voucher FOREIGN KEY (voucher_id) REFERENCES financial_vouchers(id),
        CONSTRAINT FK_payment_records_creator FOREIGN KEY (created_by) REFERENCES users(id),
        CONSTRAINT UQ_payment_records_number_area UNIQUE (payment_number, area_id)
    );
    
    CREATE INDEX IX_payment_records_area_date ON payment_records(area_id, payment_date);
    CREATE INDEX IX_payment_records_payable ON payment_records(payable_id);
    CREATE INDEX IX_payment_records_supplier ON payment_records(supplier_id);
END

-- 6. 为入库单表添加财务相关字段（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'is_financial_confirmed')
BEGIN
    ALTER TABLE stock_ins ADD is_financial_confirmed BIT NOT NULL DEFAULT 0;
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'payable_id')
BEGIN
    ALTER TABLE stock_ins ADD payable_id INT NULL;
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_payable FOREIGN KEY (payable_id) REFERENCES account_payables(id);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'voucher_id')
BEGIN
    ALTER TABLE stock_ins ADD voucher_id INT NULL;
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_voucher FOREIGN KEY (voucher_id) REFERENCES financial_vouchers(id);
END

-- 7. 创建触发器自动更新应付账款余额
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_payment_records_update_payable')
    DROP TRIGGER tr_payment_records_update_payable;
GO

CREATE TRIGGER tr_payment_records_update_payable
ON payment_records
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 更新相关应付账款的已付金额和余额
    UPDATE ap
    SET paid_amount = ISNULL((
        SELECT SUM(amount) 
        FROM payment_records 
        WHERE payable_id = ap.id AND status = '已确认'
    ), 0),
    balance_amount = ap.original_amount - ISNULL((
        SELECT SUM(amount) 
        FROM payment_records 
        WHERE payable_id = ap.id AND status = '已确认'
    ), 0),
    status = CASE 
        WHEN ap.original_amount <= ISNULL((
            SELECT SUM(amount) 
            FROM payment_records 
            WHERE payable_id = ap.id AND status = '已确认'
        ), 0) THEN '已付款'
        WHEN ISNULL((
            SELECT SUM(amount) 
            FROM payment_records 
            WHERE payable_id = ap.id AND status = '已确认'
        ), 0) > 0 THEN '部分付款'
        ELSE '未付款'
    END,
    updated_at = GETDATE()
    FROM account_payables ap
    WHERE ap.id IN (
        SELECT DISTINCT payable_id FROM inserted
        UNION
        SELECT DISTINCT payable_id FROM deleted
    );
END;
GO

-- 8. 创建触发器自动更新凭证总金额
IF EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_voucher_details_update_total')
    DROP TRIGGER tr_voucher_details_update_total;
GO

CREATE TRIGGER tr_voucher_details_update_total
ON voucher_details
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- 更新相关凭证的总金额
    UPDATE fv
    SET total_amount = ISNULL((
        SELECT SUM(debit_amount) 
        FROM voucher_details 
        WHERE voucher_id = fv.id
    ), 0),
    updated_at = GETDATE()
    FROM financial_vouchers fv
    WHERE fv.id IN (
        SELECT DISTINCT voucher_id FROM inserted
        UNION
        SELECT DISTINCT voucher_id FROM deleted
    );
END;
GO

PRINT '财务系统表结构创建完成！';
