-- =============================================
-- 财务系统基础数据插入脚本
-- 包含标准会计科目和基础配置
-- =============================================

USE [StudentsCMSSP]
GO

PRINT '开始插入财务系统基础数据...'
PRINT '========================================'

-- =============================================
-- 插入标准会计科目（适用于学校食堂）
-- =============================================

PRINT '插入标准会计科目...'

-- 获取所有学校的区域ID
DECLARE @area_cursor CURSOR
DECLARE @area_id INT
DECLARE @area_name NVARCHAR(100)
DECLARE @admin_user_id INT = 1  -- 假设系统管理员ID为1，实际使用时需要调整

SET @area_cursor = CURSOR FOR
SELECT id, name FROM administrative_areas WHERE level = 3 AND status = 1

OPEN @area_cursor
FETCH NEXT FROM @area_cursor INTO @area_id, @area_name

WHILE @@FETCH_STATUS = 0
BEGIN
    PRINT '为学校 [' + @area_name + '] (ID: ' + CAST(@area_id AS NVARCHAR) + ') 插入会计科目...'
    
    -- 1. 资产类科目
    -- 1001 库存现金
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '1001')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('1001', '库存现金', NULL, 1, '资产', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 1002 银行存款
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '1002')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('1002', '银行存款', NULL, 1, '资产', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 1121 应收账款
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '1121')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('1121', '应收账款', NULL, 1, '资产', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 1401 库存商品
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '1401')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('1401', '库存商品', NULL, 1, '资产', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 1402 原材料
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '1402')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('1402', '原材料', NULL, 1, '资产', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 1601 固定资产
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '1601')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('1601', '固定资产', NULL, 1, '资产', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 2. 负债类科目
    -- 2201 应付账款
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '2201')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('2201', '应付账款', NULL, 1, '负债', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 2202 应付职工薪酬
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '2202')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('2202', '应付职工薪酬', NULL, 1, '负债', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 3. 所有者权益类科目
    -- 3001 实收资本
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '3001')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('3001', '实收资本', NULL, 1, '所有者权益', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 3103 本年利润
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '3103')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('3103', '本年利润', NULL, 1, '所有者权益', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 4. 收入类科目
    -- 6001 主营业务收入
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6001')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6001', '主营业务收入', NULL, 1, '收入', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 6001001 学生餐费收入
    DECLARE @main_income_id INT
    SELECT @main_income_id = id FROM accounting_subjects WHERE area_id = @area_id AND code = '6001'
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6001001')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6001001', '学生餐费收入', @main_income_id, 2, '收入', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 6001002 教师餐费收入
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6001002')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6001002', '教师餐费收入', @main_income_id, 2, '收入', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 6001003 外来人员餐费收入
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6001003')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6001003', '外来人员餐费收入', @main_income_id, 2, '收入', '贷方', @area_id, 1, @admin_user_id)
    END
    
    -- 5. 费用类科目
    -- 6401 主营业务成本
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6401')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6401', '主营业务成本', NULL, 1, '费用', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 6401001 食材成本
    DECLARE @main_cost_id INT
    SELECT @main_cost_id = id FROM accounting_subjects WHERE area_id = @area_id AND code = '6401'
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6401001')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6401001', '食材成本', @main_cost_id, 2, '费用', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 6402 管理费用
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6402')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6402', '管理费用', NULL, 1, '费用', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 6402001 职工薪酬
    DECLARE @admin_expense_id INT
    SELECT @admin_expense_id = id FROM accounting_subjects WHERE area_id = @area_id AND code = '6402'
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6402001')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6402001', '职工薪酬', @admin_expense_id, 2, '费用', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 6402002 水电费
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6402002')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6402002', '水电费', @admin_expense_id, 2, '费用', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 6402003 燃气费
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6402003')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6402003', '燃气费', @admin_expense_id, 2, '费用', '借方', @area_id, 1, @admin_user_id)
    END
    
    -- 6402004 设备维护费
    IF NOT EXISTS (SELECT 1 FROM accounting_subjects WHERE area_id = @area_id AND code = '6402004')
    BEGIN
        INSERT INTO accounting_subjects (code, name, parent_id, level, subject_type, balance_direction, area_id, is_system, created_by)
        VALUES ('6402004', '设备维护费', @admin_expense_id, 2, '费用', '借方', @area_id, 1, @admin_user_id)
    END
    
    PRINT '  ✓ 学校 [' + @area_name + '] 会计科目插入完成'
    
    FETCH NEXT FROM @area_cursor INTO @area_id, @area_name
END

CLOSE @area_cursor
DEALLOCATE @area_cursor

PRINT '标准会计科目插入完成！'
PRINT ''

PRINT '========================================'
PRINT '财务系统基础数据插入完成！'
PRINT '========================================'
