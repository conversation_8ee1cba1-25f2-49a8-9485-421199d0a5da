#!/usr/bin/env python3
"""
财务系统权限初始化脚本
为现有角色添加财务相关权限
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import create_app, db
from app.models import Role
from app.utils.permissions import parse_permissions_json, format_permissions_json
import json

def init_financial_permissions():
    """为现有角色初始化财务权限"""
    app = create_app()
    
    with app.app_context():
        print("=== 财务系统权限初始化 ===\n")
        
        # 财务权限模板
        financial_permissions = {
            # 基础财务权限（适用于所有财务相关角色）
            'basic_financial': {
                '财务管理': ['view'],
                '财务报表': ['view']
            },
            
            # 财务管理员权限（适用于财务主管）
            'financial_admin': {
                '财务管理': ['view', 'create', 'edit', 'delete', 'approve', 'audit'],
                '会计科目管理': ['view', 'create', 'edit', 'delete'],
                '财务凭证管理': ['view', 'create', 'edit', 'delete', 'review', 'post'],
                '应付账款管理': ['view', 'create', 'edit', 'delete', 'payment', 'approve'],
                '收入管理': ['view', 'create', 'edit', 'delete', 'review'],
                '成本核算': ['view', 'calculate', 'review', 'adjust'],
                '财务报表': ['view', 'export', 'print', 'config']
            },
            
            # 会计权限（适用于会计人员）
            'accountant': {
                '财务管理': ['view', 'create', 'edit'],
                '会计科目管理': ['view'],
                '财务凭证管理': ['view', 'create', 'edit', 'review'],
                '应付账款管理': ['view', 'create', 'edit'],
                '收入管理': ['view', 'create', 'edit'],
                '成本核算': ['view', 'calculate'],
                '财务报表': ['view', 'export']
            },
            
            # 出纳权限（适用于出纳人员）
            'cashier': {
                '财务管理': ['view'],
                '财务凭证管理': ['view', 'create'],
                '应付账款管理': ['view', 'payment'],
                '财务报表': ['view']
            },
            
            # 采购权限（适用于采购人员）
            'purchaser': {
                '财务管理': ['view'],
                '应付账款管理': ['view', 'create'],
                '财务报表': ['view']
            },
            
            # 管理层权限（适用于校长、主任等）
            'management': {
                '财务管理': ['view'],
                '财务报表': ['view', 'export', 'print']
            }
        }
        
        # 获取所有角色
        roles = Role.query.all()
        if not roles:
            print("❌ 未找到任何角色")
            return False
        
        print(f"📋 找到 {len(roles)} 个角色:")
        for role in roles:
            print(f"   - {role.name} (ID: {role.id})")
        
        updated_roles = 0
        
        for role in roles:
            print(f"\n🔄 处理角色: {role.name}")
            
            try:
                # 解析现有权限
                current_permissions = parse_permissions_json(role.permissions or '{}')
                
                # 根据角色名称确定应该添加的财务权限
                permissions_to_add = get_permissions_for_role(role.name, financial_permissions)
                
                if not permissions_to_add:
                    print(f"   ⏭️ 跳过 - 无需添加财务权限")
                    continue
                
                # 合并权限
                updated_permissions = merge_permissions(current_permissions, permissions_to_add)
                
                # 更新角色权限
                role.permissions = format_permissions_json(updated_permissions)
                
                print(f"   ✅ 已添加财务权限:")
                for module, actions in permissions_to_add.items():
                    print(f"      - {module}: {', '.join(actions)}")
                
                updated_roles += 1
                
            except Exception as e:
                print(f"   ❌ 处理失败: {str(e)}")
        
        # 提交更改
        try:
            db.session.commit()
            print(f"\n✅ 财务权限初始化完成！")
            print(f"   📊 更新了 {updated_roles} 个角色的权限")
            
            # 显示权限统计
            show_permission_statistics(roles)
            
            return True
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 提交失败: {str(e)}")
            return False

def get_permissions_for_role(role_name, financial_permissions):
    """根据角色名称确定应该添加的财务权限"""
    
    # 管理员角色 - 完整权限
    if any(keyword in role_name for keyword in ['管理员', 'admin', '超级']):
        return financial_permissions['financial_admin']
    
    # 财务主管 - 完整财务权限
    if any(keyword in role_name for keyword in ['财务主管', '财务经理', '财务负责人']):
        return financial_permissions['financial_admin']
    
    # 会计人员 - 会计权限
    if any(keyword in role_name for keyword in ['会计', '财务会计']):
        return financial_permissions['accountant']
    
    # 出纳人员 - 出纳权限
    if any(keyword in role_name for keyword in ['出纳']):
        return financial_permissions['cashier']
    
    # 采购人员 - 采购相关权限
    if any(keyword in role_name for keyword in ['采购', '库管', '仓管']):
        return financial_permissions['purchaser']
    
    # 管理层 - 查看权限
    if any(keyword in role_name for keyword in ['校长', '副校长', '主任', '经理']):
        return financial_permissions['management']
    
    # 财务相关角色 - 基础权限
    if any(keyword in role_name for keyword in ['财务']):
        return financial_permissions['basic_financial']
    
    # 其他角色不添加财务权限
    return {}

def merge_permissions(current_permissions, new_permissions):
    """合并权限，避免重复"""
    merged = current_permissions.copy()
    
    for module, actions in new_permissions.items():
        if module in merged:
            # 合并操作权限，避免重复
            existing_actions = set(merged[module])
            new_actions = set(actions)
            merged[module] = list(existing_actions | new_actions)
        else:
            # 新模块
            merged[module] = actions
    
    return merged

def show_permission_statistics(roles):
    """显示权限统计信息"""
    print(f"\n📈 财务权限统计:")
    
    financial_modules = [
        '财务管理', '会计科目管理', '财务凭证管理', 
        '应付账款管理', '收入管理', '成本核算', '财务报表'
    ]
    
    for role in roles:
        try:
            permissions = parse_permissions_json(role.permissions or '{}')
            financial_module_count = sum(1 for module in financial_modules if module in permissions)
            
            if financial_module_count > 0:
                print(f"   {role.name}: {financial_module_count}/{len(financial_modules)} 个财务模块")
                
                # 显示具体权限
                for module in financial_modules:
                    if module in permissions:
                        actions = permissions[module]
                        if isinstance(actions, list):
                            print(f"     - {module}: {', '.join(actions)}")
                        else:
                            print(f"     - {module}: {actions}")
            
        except Exception as e:
            print(f"   {role.name}: 权限解析失败 - {str(e)}")

def show_usage_guide():
    """显示使用指南"""
    print("\n📚 财务权限使用指南:")
    print("1. 访问后台管理 -> 系统 -> 角色权限管理")
    print("2. 查看和调整各角色的财务权限")
    print("3. 为用户分配合适的角色")
    print("4. 用户登录后即可使用相应的财务功能")
    
    print("\n🔑 权限说明:")
    print("   - view: 查看权限")
    print("   - create: 创建权限")
    print("   - edit: 编辑权限")
    print("   - delete: 删除权限")
    print("   - approve: 审批权限")
    print("   - review: 审核权限")
    print("   - payment: 付款权限")
    print("   - export: 导出权限")

if __name__ == '__main__':
    print("财务系统权限初始化工具")
    print("=" * 50)
    
    # 初始化财务权限
    success = init_financial_permissions()
    
    if success:
        # 显示使用指南
        show_usage_guide()
        
        print("\n🎉 财务系统权限初始化完成！")
    else:
        print("\n❌ 初始化失败，请检查错误信息")
        sys.exit(1)
